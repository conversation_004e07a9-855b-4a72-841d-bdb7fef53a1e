/* Reset ve <PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: #f7f7f8;
    min-height: 100vh;
    color: #374151;
    line-height: 1.6;
}

.container {
    height: 100vh;
    display: flex;
    background: #f7f7f8;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: #171717;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2d2d2d;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #2d2d2d;
}

.new-chat-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: 1px solid #4d4d4d;
    color: white;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.new-chat-btn:hover {
    background: #2d2d2d;
    border-color: #6d6d6d;
}

.sidebar-content {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.model-selector-sidebar {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.model-selector-sidebar label {
    font-size: 0.85rem;
    color: #a1a1aa;
}

.model-selector-sidebar select {
    background: #2d2d2d;
    border: 1px solid #4d4d4d;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.85rem;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.header h1 i {
    margin-right: 0.5rem;
    color: #6366f1;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-sidebar {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: #6b7280;
    border-radius: 0.375rem;
}

.toggle-sidebar:hover {
    background: #f3f4f6;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #6b7280;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #fbbf24;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #10b981;
}

.status-dot.disconnected {
    background: #ef4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    background: white;
    position: relative;
}

/* Welcome Screen */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-icon {
    font-size: 4rem;
    color: #6366f1;
    margin-bottom: 1.5rem;
}

.welcome-content h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
}

.welcome-content p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.example-prompts h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

.prompt-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.example-btn {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    padding: 1rem;
    border-radius: 0.75rem;
    cursor: pointer;
    text-align: left;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    color: #374151;
}

.example-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.example-btn i {
    color: #6366f1;
    font-size: 1.1rem;
}

.chat-messages {
    max-width: 800px;
    margin: 0 auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 100%;
}

/* Messages */
.message {
    display: flex;
    gap: 1rem;
    max-width: 100%;
    animation: slideIn 0.3s ease-out;
}

.user-message {
    justify-content: flex-end;
}

.bot-message {
    justify-content: flex-start;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.bot-message .message-avatar {
    background: #6366f1;
    color: white;
}

.user-message .message-avatar {
    background: #10b981;
    color: white;
}

.message-content {
    max-width: 70%;
    min-width: 0;
}

.message-text {
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    line-height: 1.6;
    word-wrap: break-word;
    font-size: 0.95rem;
}

.bot-message .message-text {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    color: #374151;
    border-bottom-left-radius: 0.5rem;
}

.user-message .message-text {
    background: #6366f1;
    color: white;
    border-bottom-right-radius: 0.5rem;
}

.message-time {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.5rem;
    padding: 0 1.25rem;
}

.user-message .message-time {
    text-align: right;
}

/* Markdown Support */
.message-text h1, .message-text h2, .message-text h3 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
}

.message-text h1 { font-size: 1.25rem; }
.message-text h2 { font-size: 1.1rem; }
.message-text h3 { font-size: 1rem; }

.message-text p {
    margin: 0.5rem 0;
}

.message-text ul, .message-text ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-text li {
    margin: 0.25rem 0;
}

.message-text code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
}

.user-message .message-text code {
    background: rgba(255, 255, 255, 0.2);
}

.message-text pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.75rem 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
}

.message-text pre code {
    background: none;
    padding: 0;
    color: inherit;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    gap: 0.75rem;
    max-width: 85%;
    align-self: flex-start;
}

.typing-dots {
    background: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #64748b;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Input Area */
.input-area {
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem;
    position: sticky;
    bottom: 0;
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 1.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.2s;
}

.input-container:focus-within {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0.5rem 0;
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    color: #374151;
}

#messageInput:focus {
    outline: none;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.send-button {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: #6366f1;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #5b21b6;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    max-width: 800px;
    margin: 0.75rem auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.input-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: #9ca3af;
}

.char-counter {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #4facfe;
    margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        height: 100vh;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 50;
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        width: 100%;
    }

    .toggle-sidebar {
        display: block;
    }

    .header {
        padding: 1rem;
    }

    .header h1 {
        font-size: 1.1rem;
    }

    .input-area {
        padding: 1rem;
    }

    .chat-messages {
        padding: 1rem;
    }

    .message-content {
        max-width: 85%;
    }

    .prompt-examples {
        grid-template-columns: 1fr;
    }

    .welcome-content {
        padding: 1rem;
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }
}

/* Scrollbar Styling */
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #6366f1;
    margin-bottom: 1rem;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}
