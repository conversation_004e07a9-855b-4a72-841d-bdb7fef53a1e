# SynthLlama Agent 🤖

Modern ve kullan<PERSON><PERSON><PERSON> dostu Ollama Live Agent chatbot uygulaması.

## 🚀 Özellikler

- **Real-time Chat**: Canlı mesajlaşma deneyimi
- **Streaming Responses**: Yazma efekti ile AI cevapları
- **Modern Arayüz**: Responsive ve güzel tasarım
- **Model Seçimi**: Farklı Ollama modelleri arasında seçim
- **Bağlantı Kontrolü**: Ollama servis durumu takibi
- **Karakter <PERSON>**: Mesaj uzunluğu göstergesi

## 📋 Gereksinimler

- Python 3.7+
- Ollama (kurulu ve çalışır durumda)
- Modern web tarayıcısı

## 🛠️ Kurulum

1. **Bağımlılıkları yükleyin:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Ollama servisini başlatın:**
   ```bash
   ollama serve
   ```

3. **Uygulamayı çalıştırın:**
   ```bash
   python app.py
   ```

4. **Tarayı<PERSON>ıda açın:**
   ```
   http://localhost:5000
   ```

## 📁 Proje Yapısı

```
synthllama-agent/
├── app.py              # Flask backend server
├── requirements.txt    # Python bağımlılıkları
├── static/
│   ├── style.css      # CSS stilleri
│   └── script.js      # Frontend JavaScript
├── templates/
│   └── index.html     # Ana sayfa template
└── README.md          # Bu dosya
```

## 🎯 Kullanım

1. Uygulamayı başlattıktan sonra web arayüzünde mesajınızı yazın
2. Enter tuşuna basarak gönderin (Shift+Enter ile yeni satır)
3. AI'ın cevabını real-time olarak izleyin
4. Sağ alttaki dropdown'dan farklı modeller seçebilirsiniz

## 🔧 Yapılandırma

- **Varsayılan Model**: `qwen2.5-coder:3b`
- **Port**: `5000`
- **Ollama URL**: `http://localhost:11434`

Bu ayarları `app.py` dosyasından değiştirebilirsiniz.

## 📝 Notlar

- Ollama servisinin çalıştığından emin olun
- İlk kullanımda model indirme işlemi zaman alabilir
- Bağlantı durumu sağ üst köşede gösterilir

## 🤝 Katkıda Bulunma

Bu proje açık kaynak kodludur. Katkılarınızı bekliyoruz!

---

**SynthLlama Agent** - Ollama ile güçlendirilmiş modern chatbot deneyimi ✨
