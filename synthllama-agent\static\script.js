// DOM Elements
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const typingIndicator = document.getElementById('typingIndicator');
const statusDot = document.getElementById('status-dot');
const statusText = document.getElementById('status-text');
const charCount = document.getElementById('charCount');
const loadingOverlay = document.getElementById('loadingOverlay');
const welcomeScreen = document.getElementById('welcomeScreen');
const newChatBtn = document.getElementById('newChatBtn');
const toggleSidebarBtn = document.getElementById('toggleSidebar');
const sidebar = document.querySelector('.sidebar');
const screenshotBtn = document.getElementById('screenshotBtn');
const cameraBtn = document.getElementById('cameraBtn');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const removeImageBtn = document.getElementById('removeImage');

// State
let isTyping = false;
let currentEventSource = null;
let conversationStarted = false;
let currentImage = null;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

function initializeApp() {
    // Hide loading overlay
    if (loadingOverlay) loadingOverlay.style.display = 'none';

    // Check connection
    checkConnection();

    // Focus input
    messageInput.focus();

    // Setup marked.js for markdown parsing
    if (typeof marked !== 'undefined') {
        marked.setOptions({
            highlight: function(code, lang) {
                if (typeof Prism !== 'undefined' && Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            },
            breaks: true,
            gfm: true
        });
    }
}

function setupEventListeners() {
    // Send button click
    sendButton.addEventListener('click', sendMessage);

    // Enter key to send (Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';

        // Update character count
        charCount.textContent = this.value.length;

        // Enable/disable send button
        sendButton.disabled = this.value.trim().length === 0;
    });

    // Vision controls
    if (screenshotBtn) {
        screenshotBtn.addEventListener('click', takeScreenshot);
    }

    if (cameraBtn) {
        cameraBtn.addEventListener('click', takeCamera);
    }

    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeImage);
    }

    // New chat button
    if (newChatBtn) {
        newChatBtn.addEventListener('click', startNewChat);
    }

    // Toggle sidebar (mobile)
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }

    // Example prompt buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('example-btn') || e.target.closest('.example-btn')) {
            const btn = e.target.classList.contains('example-btn') ? e.target : e.target.closest('.example-btn');
            const prompt = btn.getAttribute('data-prompt');
            if (prompt) {
                messageInput.value = prompt;
                messageInput.focus();
                sendMessage();
            }
        }
    });

    // Close sidebar when clicking outside (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && sidebar.classList.contains('open')) {
            if (!sidebar.contains(e.target) && !toggleSidebarBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        }
    });
}

function startNewChat() {
    // Clear chat messages
    chatMessages.innerHTML = '';
    chatMessages.style.display = 'none';
    welcomeScreen.style.display = 'flex';
    conversationStarted = false;

    // Clear input and image
    messageInput.value = '';
    charCount.textContent = '0';
    sendButton.disabled = true;
    removeImage();

    // Call API to start new chat
    fetch('/api/new-chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('New chat started:', data.conversation_id);
        }
    })
    .catch(error => {
        console.error('Error starting new chat:', error);
    });
}

async function takeScreenshot() {
    try {
        screenshotBtn.disabled = true;
        screenshotBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Alınıyor...';

        const response = await fetch('/api/screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            currentImage = data.image;
            showImagePreview(`data:image/png;base64,${data.image}`);
        } else {
            alert('Ekran görüntüsü alınamadı: ' + data.error);
        }
    } catch (error) {
        console.error('Screenshot error:', error);
        alert('Ekran görüntüsü alınamadı');
    } finally {
        screenshotBtn.disabled = false;
        screenshotBtn.innerHTML = '<i class="fas fa-desktop"></i> Ekran Görüntüsü';
    }
}

async function takeCamera() {
    try {
        cameraBtn.disabled = true;
        cameraBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Çekiliyor...';

        const response = await fetch('/api/camera', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            currentImage = data.image;
            showImagePreview(`data:image/png;base64,${data.image}`);
        } else {
            alert('Kamera fotoğrafı çekilemedi: ' + data.error);
        }
    } catch (error) {
        console.error('Camera error:', error);
        alert('Kamera fotoğrafı çekilemedi');
    } finally {
        cameraBtn.disabled = false;
        cameraBtn.innerHTML = '<i class="fas fa-camera"></i> Kamera Fotoğrafı';
    }
}

function showImagePreview(imageSrc) {
    previewImg.src = imageSrc;
    imagePreview.style.display = 'block';
}

function removeImage() {
    currentImage = null;
    imagePreview.style.display = 'none';
    previewImg.src = '';
}

async function checkConnection() {
    try {
        const response = await fetch('/api/health');
        const data = await response.json();
        
        if (data.success) {
            updateStatus('connected', 'Bağlı');
        } else {
            updateStatus('disconnected', 'Bağlantı hatası');
        }
    } catch (error) {
        updateStatus('disconnected', 'Bağlantı hatası');
        console.error('Connection check failed:', error);
    }
}

// Model seçimi kaldırıldı - sadece Gemma 3N kullanılıyor

function updateStatus(status, text) {
    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
}

async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;

    // Hide welcome screen and show chat
    if (!conversationStarted) {
        welcomeScreen.style.display = 'none';
        chatMessages.style.display = 'flex';
        conversationStarted = true;
    }

    // Add user message
    addMessage(message, 'user');

    // Clear input and image
    messageInput.value = '';
    messageInput.style.height = 'auto';
    charCount.textContent = '0';
    sendButton.disabled = true;

    // Clear image after sending
    removeImage();

    // Show typing indicator
    showTypingIndicator();

    try {
        // Send to API with streaming
        await sendStreamingMessage(message);
    } catch (error) {
        hideTypingIndicator();
        addMessage('Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.', 'bot');
        console.error('Send message error:', error);
    }
}

async function sendStreamingMessage(message) {
    const selectedModel = 'gemma3n:e4b';  // Sadece gemma3n kullan
    
    // Close any existing connection
    if (currentEventSource) {
        currentEventSource.close();
    }
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                model: selectedModel,
                image: currentImage  // Görsel varsa ekle
            })
        });
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        // Hide typing indicator and prepare for streaming
        hideTypingIndicator();
        const botMessageElement = addMessage('', 'bot');
        const messageTextElement = botMessageElement.querySelector('.message-text');
        
        // Read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        
                        if (data.text) {
                            messageTextElement.textContent += data.text;
                            scrollToBottom();
                        }
                        
                        if (data.done) {
                            isTyping = false;
                            return;
                        }
                        
                        if (data.error) {
                            messageTextElement.textContent = 'Hata: ' + data.error;
                            isTyping = false;
                            return;
                        }
                    } catch (e) {
                        console.error('Error parsing SSE data:', e);
                    }
                }
            }
        }
        
    } catch (error) {
        console.error('Streaming error:', error);
        throw error;
    } finally {
        isTyping = false;
    }
}

function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const currentTime = new Date().toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
    });

    // Process text for markdown if it's from bot
    let processedText = text;
    if (sender === 'bot' && typeof marked !== 'undefined' && text.trim()) {
        try {
            processedText = marked.parse(text);
        } catch (e) {
            console.warn('Markdown parsing failed:', e);
            processedText = text;
        }
    } else {
        // Escape HTML for user messages
        processedText = text.replace(/&/g, '&amp;')
                          .replace(/</g, '&lt;')
                          .replace(/>/g, '&gt;')
                          .replace(/"/g, '&quot;')
                          .replace(/'/g, '&#039;');
    }

    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas ${sender === 'user' ? 'fa-user' : 'fa-robot'}"></i>
        </div>
        <div class="message-content">
            <div class="message-text">${processedText}</div>
            <div class="message-time">${currentTime}</div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Highlight code blocks if Prism is available
    if (typeof Prism !== 'undefined') {
        Prism.highlightAllUnder(messageDiv);
    }

    scrollToBottom();

    return messageDiv;
}

function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    scrollToBottom();
}

function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (currentEventSource) {
        currentEventSource.close();
    }
});

// Periodic connection check
setInterval(checkConnection, 30000); // Check every 30 seconds
