// DOM Elements
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const typingIndicator = document.getElementById('typingIndicator');
const statusDot = document.getElementById('status-dot');
const statusText = document.getElementById('status-text');
const modelSelectSidebar = document.getElementById('modelSelectSidebar');
const charCount = document.getElementById('charCount');
const loadingOverlay = document.getElementById('loadingOverlay');
const welcomeScreen = document.getElementById('welcomeScreen');
const newChatBtn = document.getElementById('newChatBtn');
const toggleSidebarBtn = document.getElementById('toggleSidebar');
const sidebar = document.querySelector('.sidebar');

// State
let isTyping = false;
let currentEventSource = null;
let conversationStarted = false;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

function initializeApp() {
    // Hide loading overlay
    if (loadingOverlay) loadingOverlay.style.display = 'none';

    // Check connection
    checkConnection();

    // Load available models
    loadModels();

    // Focus input
    messageInput.focus();

    // Setup marked.js for markdown parsing
    if (typeof marked !== 'undefined') {
        marked.setOptions({
            highlight: function(code, lang) {
                if (typeof Prism !== 'undefined' && Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            },
            breaks: true,
            gfm: true
        });
    }
}

function setupEventListeners() {
    // Send button click
    sendButton.addEventListener('click', sendMessage);

    // Enter key to send (Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';

        // Update character count
        charCount.textContent = this.value.length;

        // Enable/disable send button
        sendButton.disabled = this.value.trim().length === 0;
    });

    // Model selection change
    if (modelSelectSidebar) {
        modelSelectSidebar.addEventListener('change', function() {
            console.log('Model changed to:', this.value);
        });
    }

    // New chat button
    if (newChatBtn) {
        newChatBtn.addEventListener('click', startNewChat);
    }

    // Toggle sidebar (mobile)
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }

    // Example prompt buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('example-btn') || e.target.closest('.example-btn')) {
            const btn = e.target.classList.contains('example-btn') ? e.target : e.target.closest('.example-btn');
            const prompt = btn.getAttribute('data-prompt');
            if (prompt) {
                messageInput.value = prompt;
                messageInput.focus();
                sendMessage();
            }
        }
    });

    // Close sidebar when clicking outside (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && sidebar.classList.contains('open')) {
            if (!sidebar.contains(e.target) && !toggleSidebarBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        }
    });
}

function startNewChat() {
    // Clear chat messages
    chatMessages.innerHTML = '';
    chatMessages.style.display = 'none';
    welcomeScreen.style.display = 'flex';
    conversationStarted = false;

    // Clear input
    messageInput.value = '';
    charCount.textContent = '0';
    sendButton.disabled = true;

    // Call API to start new chat
    fetch('/api/new-chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('New chat started:', data.conversation_id);
        }
    })
    .catch(error => {
        console.error('Error starting new chat:', error);
    });
}

async function checkConnection() {
    try {
        const response = await fetch('/api/health');
        const data = await response.json();
        
        if (data.success) {
            updateStatus('connected', 'Bağlı');
        } else {
            updateStatus('disconnected', 'Bağlantı hatası');
        }
    } catch (error) {
        updateStatus('disconnected', 'Bağlantı hatası');
        console.error('Connection check failed:', error);
    }
}

async function loadModels() {
    try {
        const response = await fetch('/api/models');
        const data = await response.json();

        if (data.success && data.models.models) {
            if (modelSelectSidebar) {
                modelSelectSidebar.innerHTML = '';
                data.models.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    // Daha güzel görünüm için model adını düzenle
                    option.textContent = model.name === 'gemma3n:e4b' ? 'Gemma 3N (7.5B)' : model.name;
                    modelSelectSidebar.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Failed to load models:', error);
        // Hata durumunda varsayılan olarak Gemma 3N'i göster
        if (modelSelectSidebar) {
            modelSelectSidebar.innerHTML = '<option value="gemma3n:e4b">Gemma 3N (7.5B)</option>';
        }
    }
}

function updateStatus(status, text) {
    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
}

async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;

    // Hide welcome screen and show chat
    if (!conversationStarted) {
        welcomeScreen.style.display = 'none';
        chatMessages.style.display = 'flex';
        conversationStarted = true;
    }

    // Add user message
    addMessage(message, 'user');

    // Clear input
    messageInput.value = '';
    messageInput.style.height = 'auto';
    charCount.textContent = '0';
    sendButton.disabled = true;

    // Show typing indicator
    showTypingIndicator();

    try {
        // Send to API with streaming
        await sendStreamingMessage(message);
    } catch (error) {
        hideTypingIndicator();
        addMessage('Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.', 'bot');
        console.error('Send message error:', error);
    }
}

async function sendStreamingMessage(message) {
    const selectedModel = modelSelectSidebar ? modelSelectSidebar.value : 'gemma3n:e4b';
    
    // Close any existing connection
    if (currentEventSource) {
        currentEventSource.close();
    }
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                model: selectedModel
            })
        });
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        // Hide typing indicator and prepare for streaming
        hideTypingIndicator();
        const botMessageElement = addMessage('', 'bot');
        const messageTextElement = botMessageElement.querySelector('.message-text');
        
        // Read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        
                        if (data.text) {
                            messageTextElement.textContent += data.text;
                            scrollToBottom();
                        }
                        
                        if (data.done) {
                            isTyping = false;
                            return;
                        }
                        
                        if (data.error) {
                            messageTextElement.textContent = 'Hata: ' + data.error;
                            isTyping = false;
                            return;
                        }
                    } catch (e) {
                        console.error('Error parsing SSE data:', e);
                    }
                }
            }
        }
        
    } catch (error) {
        console.error('Streaming error:', error);
        throw error;
    } finally {
        isTyping = false;
    }
}

function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const currentTime = new Date().toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
    });

    // Process text for markdown if it's from bot
    let processedText = text;
    if (sender === 'bot' && typeof marked !== 'undefined' && text.trim()) {
        try {
            processedText = marked.parse(text);
        } catch (e) {
            console.warn('Markdown parsing failed:', e);
            processedText = text;
        }
    } else {
        // Escape HTML for user messages
        processedText = text.replace(/&/g, '&amp;')
                          .replace(/</g, '&lt;')
                          .replace(/>/g, '&gt;')
                          .replace(/"/g, '&quot;')
                          .replace(/'/g, '&#039;');
    }

    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas ${sender === 'user' ? 'fa-user' : 'fa-robot'}"></i>
        </div>
        <div class="message-content">
            <div class="message-text">${processedText}</div>
            <div class="message-time">${currentTime}</div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Highlight code blocks if Prism is available
    if (typeof Prism !== 'undefined') {
        Prism.highlightAllUnder(messageDiv);
    }

    scrollToBottom();

    return messageDiv;
}

function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    scrollToBottom();
}

function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (currentEventSource) {
        currentEventSource.close();
    }
});

// Periodic connection check
setInterval(checkConnection, 30000); // Check every 30 seconds
