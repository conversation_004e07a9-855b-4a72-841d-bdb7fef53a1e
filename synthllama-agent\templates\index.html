<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SynthLlama Agent</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <button id="newChatBtn" class="new-chat-btn">
                    <i class="fas fa-plus"></i>
                    Yeni Sohbet
                </button>
            </div>

            <div class="sidebar-content">
                <div class="model-selector-sidebar">
                    <label for="modelSelectSidebar">Model:</label>
                    <select id="modelSelectSidebar">
                        <option value="gemma3n:e4b">Gemma 3N (7.5B)</option>
                    </select>
                </div>

                <div class="status-indicator">
                    <span id="status-dot" class="status-dot"></span>
                    <span id="status-text">Bağlantı kontrol ediliyor...</span>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <h1><i class="fas fa-robot"></i> SynthLlama Agent</h1>
                    <div class="header-actions">
                        <button id="toggleSidebar" class="toggle-sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Chat Container -->
            <div class="chat-container">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h2>SynthLlama Agent'a Hoş Geldiniz!</h2>
                        <p>Ben size yardımcı olmak için buradayım. Sorularınızı sorun, kod yazabilirim, problemlerinizi çözebilirim.</p>

                        <div class="example-prompts">
                            <h3>Örnek sorular:</h3>
                            <div class="prompt-examples">
                                <button class="example-btn" data-prompt="Python'da bir web scraper nasıl yazarım?">
                                    <i class="fas fa-code"></i>
                                    Python web scraper yazma
                                </button>
                                <button class="example-btn" data-prompt="React'te state management nasıl yapılır?">
                                    <i class="fab fa-react"></i>
                                    React state management
                                </button>
                                <button class="example-btn" data-prompt="SQL'de JOIN işlemleri nasıl çalışır?">
                                    <i class="fas fa-database"></i>
                                    SQL JOIN işlemleri
                                </button>
                                <button class="example-btn" data-prompt="Machine learning'e nasıl başlarım?">
                                    <i class="fas fa-brain"></i>
                                    Machine learning başlangıç
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages" style="display: none;">
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <div class="input-container">
                    <textarea
                        id="messageInput"
                        placeholder="Mesajınızı yazın... (Shift+Enter ile yeni satır)"
                        rows="1"
                        maxlength="4000"
                    ></textarea>
                    <button id="sendButton" class="send-button" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <div class="input-info">
                        <span class="char-counter">
                            <span id="charCount">0</span>/4000
                        </span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-cog fa-spin"></i>
            <p>Yükleniyor...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
