from flask import Flask, render_template, request, jsonify, Response
from flask_cors import CORS
import requests
import json
import time

app = Flask(__name__)
CORS(app)

# Ollama API ayarları
OLLAMA_URL = "http://localhost:11434"
DEFAULT_MODEL = "qwen2.5-coder:3b"  # Sisteminizde mevcut olan model

@app.route('/')
def index():
    """Ana sayfa"""
    return render_template('index.html')

@app.route('/api/models', methods=['GET'])
def get_models():
    """Mevcut Ollama modellerini getir"""
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags")
        if response.status_code == 200:
            models = response.json()
            return jsonify({"success": True, "models": models})
        else:
            return jsonify({"success": False, "error": "Modeller alınamadı"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/chat', methods=['POST'])
def chat():
    """Chat endpoint - streaming response"""
    try:
        data = request.json
        message = data.get('message', '')
        model = data.get('model', DEFAULT_MODEL)

        if not message:
            return jsonify({"success": False, "error": "Mesaj boş olamaz"})

        # Ollama API'sine istek gönder
        payload = {
            "model": model,
            "prompt": message,
            "stream": True
        }

        def generate():
            try:
                response = requests.post(
                    f"{OLLAMA_URL}/api/generate",
                    json=payload,
                    stream=True
                )

                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            try:
                                chunk = json.loads(line.decode('utf-8'))
                                if 'response' in chunk:
                                    yield f"data: {json.dumps({'text': chunk['response']})}\n\n"
                                if chunk.get('done', False):
                                    yield f"data: {json.dumps({'done': True})}\n\n"
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"data: {json.dumps({'error': 'Ollama API hatası'})}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        return Response(generate(), mimetype='text/event-stream')

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/health', methods=['GET'])
def health_check():
    """Ollama bağlantısını kontrol et"""
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            return jsonify({"success": True, "status": "Ollama bağlantısı başarılı"})
        else:
            return jsonify({"success": False, "status": "Ollama bağlantı hatası"})
    except Exception as e:
        return jsonify({"success": False, "status": f"Bağlantı hatası: {str(e)}"})

if __name__ == '__main__':
    print("🚀 Ollama Live Agent başlatılıyor...")
    print("📡 Ollama bağlantısı kontrol ediliyor...")

    # Ollama bağlantısını kontrol et
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama bağlantısı başarılı!")
            models = response.json()
            print(f"📋 Mevcut modeller: {[model['name'] for model in models.get('models', [])]}")
        else:
            print("❌ Ollama bağlantı hatası!")
    except Exception as e:
        print(f"❌ Ollama bağlantı hatası: {e}")
        print("💡 Ollama'nın çalıştığından emin olun: 'ollama serve'")

    print("🌐 Web arayüzü: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)