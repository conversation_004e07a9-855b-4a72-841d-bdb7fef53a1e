from flask import Flask, render_template, request, jsonify, Response, session
from flask_cors import CORS
import requests
import json
import time
import uuid
from datetime import datetime

app = Flask(__name__)
CORS(app)
app.secret_key = 'synthllama-secret-key-2024'

# Ollama API ayarları
OLLAMA_URL = "http://localhost:11434"
DEFAULT_MODEL = "qwen2.5-coder:3b"  # Sisteminizde mevcut olan model

# Konuşma geçmişi için in-memory storage (production'da database kullanın)
conversations = {}

# Sistem promptu - ChatGPT benzeri davranış için
SYSTEM_PROMPT = """Sen yardımcı, zararsız ve dürüst bir AI asistanısın. Kullanıcılara en iyi şekilde yardım etmeye odaklanıyorsun.

Özellikler:
- Türkçe ve İngilizce dillerinde akıcı şekilde konuşabilirsin
- <PERSON><PERSON> yaza<PERSON>, açıklayabilir ve debug edebilirsin
- Karmaşık konuları basit şekilde açıklayabilirsin
- Yaratıcı ve analitik düşünebilirsin
- Her zaman kibar ve profesyonel bir dil kullanırsın

Lütfen kullanıcının sorularını dikkatli şekilde oku ve en yararlı cevabı ver."""

@app.route('/')
def index():
    """Ana sayfa"""
    # Yeni session için conversation ID oluştur
    if 'conversation_id' not in session:
        session['conversation_id'] = str(uuid.uuid4())
        conversations[session['conversation_id']] = []

    return render_template('index.html')

@app.route('/api/new-chat', methods=['POST'])
def new_chat():
    """Yeni sohbet başlat"""
    conversation_id = str(uuid.uuid4())
    session['conversation_id'] = conversation_id
    conversations[conversation_id] = []
    return jsonify({"success": True, "conversation_id": conversation_id})

@app.route('/api/conversation-history', methods=['GET'])
def get_conversation_history():
    """Mevcut konuşma geçmişini getir"""
    conversation_id = session.get('conversation_id')
    if conversation_id and conversation_id in conversations:
        return jsonify({"success": True, "history": conversations[conversation_id]})
    return jsonify({"success": True, "history": []})

@app.route('/api/models', methods=['GET'])
def get_models():
    """Mevcut Ollama modellerini getir"""
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags")
        if response.status_code == 200:
            models = response.json()
            return jsonify({"success": True, "models": models})
        else:
            return jsonify({"success": False, "error": "Modeller alınamadı"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/chat', methods=['POST'])
def chat():
    """Chat endpoint - streaming response with conversation history"""
    try:
        data = request.json
        message = data.get('message', '')
        model = data.get('model', DEFAULT_MODEL)
        conversation_id = session.get('conversation_id')

        if not message:
            return jsonify({"success": False, "error": "Mesaj boş olamaz"})

        # Konuşma geçmişini al
        if conversation_id not in conversations:
            conversations[conversation_id] = []

        conversation_history = conversations[conversation_id]

        # Kullanıcı mesajını geçmişe ekle
        user_message = {
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        }
        conversation_history.append(user_message)

        # Konuşma geçmişini prompt olarak hazırla
        full_prompt = SYSTEM_PROMPT + "\n\n"

        # Son 10 mesajı dahil et (memory management)
        recent_history = conversation_history[-20:] if len(conversation_history) > 20 else conversation_history

        for msg in recent_history[:-1]:  # Son mesaj hariç (zaten ekledik)
            if msg["role"] == "user":
                full_prompt += f"Kullanıcı: {msg['content']}\n"
            else:
                full_prompt += f"Asistan: {msg['content']}\n"

        full_prompt += f"Kullanıcı: {message}\nAsistan: "

        # Ollama API'sine istek gönder
        payload = {
            "model": model,
            "prompt": full_prompt,
            "stream": True,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40
            }
        }

        def generate():
            assistant_response = ""
            try:
                response = requests.post(
                    f"{OLLAMA_URL}/api/generate",
                    json=payload,
                    stream=True
                )

                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            try:
                                chunk = json.loads(line.decode('utf-8'))
                                if 'response' in chunk:
                                    text_chunk = chunk['response']
                                    assistant_response += text_chunk
                                    yield f"data: {json.dumps({'text': text_chunk})}\n\n"
                                if chunk.get('done', False):
                                    # Asistan cevabını geçmişe ekle
                                    assistant_message = {
                                        "role": "assistant",
                                        "content": assistant_response,
                                        "timestamp": datetime.now().isoformat()
                                    }
                                    conversation_history.append(assistant_message)
                                    yield f"data: {json.dumps({'done': True})}\n\n"
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"data: {json.dumps({'error': 'Ollama API hatası'})}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        return Response(generate(), mimetype='text/event-stream')

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/health', methods=['GET'])
def health_check():
    """Ollama bağlantısını kontrol et"""
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            return jsonify({"success": True, "status": "Ollama bağlantısı başarılı"})
        else:
            return jsonify({"success": False, "status": "Ollama bağlantı hatası"})
    except Exception as e:
        return jsonify({"success": False, "status": f"Bağlantı hatası: {str(e)}"})

if __name__ == '__main__':
    print("🚀 Ollama Live Agent başlatılıyor...")
    print("📡 Ollama bağlantısı kontrol ediliyor...")

    # Ollama bağlantısını kontrol et
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama bağlantısı başarılı!")
            models = response.json()
            print(f"📋 Mevcut modeller: {[model['name'] for model in models.get('models', [])]}")
        else:
            print("❌ Ollama bağlantı hatası!")
    except Exception as e:
        print(f"❌ Ollama bağlantı hatası: {e}")
        print("💡 Ollama'nın çalıştığından emin olun: 'ollama serve'")

    print("🌐 Web arayüzü: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)