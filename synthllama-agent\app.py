from flask import Flask, render_template, request, jsonify, Response, session
from flask_cors import CORS
import requests
import json
import uuid
import base64
import io
from datetime import datetime
from PIL import Image, ImageGrab
import cv2
import numpy as np

app = Flask(__name__)
CORS(app)
app.secret_key = 'synthllama-secret-key-2024'

# Ollama API ayarları
OLLAMA_URL = "http://localhost:11434"
DEFAULT_MODEL = "gemma3n:e4b"  # Sadece gemma3n kullan

# Konuşma geçmişi için in-memory storage (production'da database kullanın)
conversations = {}

# Sistem promptu - ChatGPT benzeri davranış için
SYSTEM_PROMPT = """Sen yardımcı, zararsız ve dürüst bir AI asistanısın. Kullanıcılara en iyi şekilde yardım etmeye odaklanıyorsun.

Özellikler:
- Türkçe ve İngilizce dillerinde akıcı şekilde konuşabilirsin
- <PERSON><PERSON>, açıklayabilir ve debug edebilirsin
- Karmaşık konuları basit şekilde açıklayabilirsin
- Yaratıcı ve analitik düşünebilirsin
- Her zaman kibar ve profesyonel bir dil kullanırsın

Lütfen kullanıcının sorularını dikkatli şekilde oku ve en yararlı cevabı ver."""

@app.route('/')
def index():
    """Ana sayfa"""
    # Yeni session için conversation ID oluştur
    if 'conversation_id' not in session:
        session['conversation_id'] = str(uuid.uuid4())
        conversations[session['conversation_id']] = []

    return render_template('index.html')

@app.route('/api/new-chat', methods=['POST'])
def new_chat():
    """Yeni sohbet başlat"""
    conversation_id = str(uuid.uuid4())
    session['conversation_id'] = conversation_id
    conversations[conversation_id] = []
    return jsonify({"success": True, "conversation_id": conversation_id})

@app.route('/api/conversation-history', methods=['GET'])
def get_conversation_history():
    """Mevcut konuşma geçmişini getir"""
    conversation_id = session.get('conversation_id')
    if conversation_id and conversation_id in conversations:
        return jsonify({"success": True, "history": conversations[conversation_id]})
    return jsonify({"success": True, "history": []})

# Model seçimi kaldırıldı - sadece gemma3n kullanılıyor

@app.route('/api/screenshot', methods=['POST'])
def take_screenshot():
    """Ekran görüntüsü al"""
    try:
        # Ekran görüntüsü al
        screenshot = ImageGrab.grab()

        # Base64'e çevir
        buffer = io.BytesIO()
        screenshot.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return jsonify({
            "success": True,
            "image": img_str,
            "message": "Ekran görüntüsü alındı"
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/camera', methods=['POST'])
def take_camera_photo():
    """Kameradan fotoğraf çek"""
    try:
        # Kamerayı aç
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            return jsonify({"success": False, "error": "Kamera açılamadı"})

        # Fotoğraf çek
        ret, frame = cap.read()
        cap.release()

        if not ret:
            return jsonify({"success": False, "error": "Fotoğraf çekilemedi"})

        # OpenCV BGR'den RGB'ye çevir
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # PIL Image'e çevir
        image = Image.fromarray(frame_rgb)

        # Base64'e çevir
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return jsonify({
            "success": True,
            "image": img_str,
            "message": "Kamera fotoğrafı çekildi"
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/chat', methods=['POST'])
def chat():
    """Chat endpoint - streaming response with conversation history"""
    try:
        data = request.json
        message = data.get('message', '')
        image_data = data.get('image', None)  # Base64 görsel verisi
        model = DEFAULT_MODEL  # Sadece gemma3n kullan
        conversation_id = session.get('conversation_id')

        if not message:
            return jsonify({"success": False, "error": "Mesaj boş olamaz"})

        # Konuşma geçmişini al
        if conversation_id not in conversations:
            conversations[conversation_id] = []

        conversation_history = conversations[conversation_id]

        # Kullanıcı mesajını geçmişe ekle
        user_message = {
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        }

        # Eğer görsel varsa ekle
        if image_data:
            user_message["image"] = image_data

        conversation_history.append(user_message)

        # Mesaj geçmişini chat API formatında hazırla
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]

        # Son 20 mesajı dahil et (memory management)
        recent_history = conversation_history[-20:] if len(conversation_history) > 20 else conversation_history

        for msg in recent_history:
            chat_msg = {
                "role": msg["role"],
                "content": msg["content"]
            }
            # Eğer görsel varsa ekle
            if "image" in msg:
                chat_msg["images"] = [msg["image"]]
            messages.append(chat_msg)

        # Ollama chat API'sine istek gönder
        payload = {
            "model": model,
            "messages": messages,
            "stream": True,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40
            }
        }

        def generate():
            assistant_response = ""
            try:
                response = requests.post(
                    f"{OLLAMA_URL}/api/chat",
                    json=payload,
                    stream=True
                )

                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            try:
                                chunk = json.loads(line.decode('utf-8'))
                                if 'message' in chunk and 'content' in chunk['message']:
                                    text_chunk = chunk['message']['content']
                                    assistant_response += text_chunk
                                    yield f"data: {json.dumps({'text': text_chunk})}\n\n"
                                if chunk.get('done', False):
                                    # Asistan cevabını geçmişe ekle
                                    assistant_message = {
                                        "role": "assistant",
                                        "content": assistant_response,
                                        "timestamp": datetime.now().isoformat()
                                    }
                                    conversation_history.append(assistant_message)
                                    yield f"data: {json.dumps({'done': True})}\n\n"
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"data: {json.dumps({'error': 'Ollama API hatası'})}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        return Response(generate(), mimetype='text/event-stream')

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/health', methods=['GET'])
def health_check():
    """Ollama bağlantısını kontrol et"""
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            return jsonify({"success": True, "status": "Ollama bağlantısı başarılı"})
        else:
            return jsonify({"success": False, "status": "Ollama bağlantı hatası"})
    except Exception as e:
        return jsonify({"success": False, "status": f"Bağlantı hatası: {str(e)}"})

if __name__ == '__main__':
    print("🚀 Ollama Live Agent başlatılıyor...")
    print("📡 Ollama bağlantısı kontrol ediliyor...")

    # Ollama bağlantısını kontrol et
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama bağlantısı başarılı!")
            models = response.json()
            print(f"📋 Mevcut modeller: {[model['name'] for model in models.get('models', [])]}")
        else:
            print("❌ Ollama bağlantı hatası!")
    except Exception as e:
        print(f"❌ Ollama bağlantı hatası: {e}")
        print("💡 Ollama'nın çalıştığından emin olun: 'ollama serve'")

    print("🌐 Web arayüzü: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)